using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Manages ship pieces on the map to visually represent ships at each world location and orbit location. World locations use standard positioning.
/// Orbit locations use different positioning: ships start at (x, y+1.5, z) and are spaced 1 unit apart in x-axis, centered around the orbit position.
/// </summary>
public class ShipMapTracker : MonoBehaviour
{
    [Header("Ship Prefabs")]
    [SerializeField] private GameObject redShipPrefab;
    [SerializeField] private GameObject greenShipPrefab;
    [SerializeField] private GameObject blueShipPrefab;
    [SerializeField] private GameObject yellowShipPrefab;
    
    [Header("Settings")]
    [SerializeField] private bool trackOrbitLocations = true;
    
    // Singleton pattern
    public static ShipMapTracker Instance { get; private set; }
    
    // Track ship pieces for each world and player (key is world/orbit location)
    private Dictionary<GameObject, Dictionary<int, List<GameObject>>> worldShipPieces = new Dictionary<GameObject, Dictionary<int, List<GameObject>>>();
    
    // Player color mapping
    private GameObject[] playerShipPrefabs = new GameObject[4];
    
    private void Awake()
    {
        // Setup singleton
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Debug.LogWarning("Multiple ShipMapTracker instances detected!");
            Destroy(gameObject);
            return;
        }
        
        // Initialize player ship prefabs array
        playerShipPrefabs[0] = redShipPrefab;
        playerShipPrefabs[1] = greenShipPrefab;
        playerShipPrefabs[2] = blueShipPrefab;
        playerShipPrefabs[3] = yellowShipPrefab;
    }
    
    private void Start()
    {
        // Initialize ship tracking for all worlds and orbit locations
        InitializeWorldShipTracking();

        // Update ship pieces for all players
        RefreshAllShipPieces();
    }

    /// <summary>
    /// Initialize the ship prefabs (called by initializer script)
    /// </summary>
    public void InitializeShipPrefabs(GameObject red, GameObject green, GameObject blue, GameObject yellow)
    {
        redShipPrefab = red;
        greenShipPrefab = green;
        blueShipPrefab = blue;
        yellowShipPrefab = yellow;

        // Update the player ship prefabs array
        playerShipPrefabs[0] = redShipPrefab;
        playerShipPrefabs[1] = greenShipPrefab;
        playerShipPrefabs[2] = blueShipPrefab;
        playerShipPrefabs[3] = yellowShipPrefab;

    }
    
    /// <summary>
    /// Initialize ship tracking dictionaries for all worlds and orbit locations
    /// </summary>
    private void InitializeWorldShipTracking()
    {
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("WorldManager not found! Cannot initialize ship tracking.");
            return;
        }

        // Get all celestial bodies (planets/moons with PlanetBody components)
        PlanetBody[] allBodies = FindObjectsByType<PlanetBody>(FindObjectsSortMode.None);

        foreach (PlanetBody body in allBodies)
        {
            GameObject worldObject = body.gameObject;

            // Skip orbit locations if tracking is disabled
            if (!trackOrbitLocations && IsOrbitLocation(worldObject.name))
                continue;

            // Initialize tracking for this world
            worldShipPieces[worldObject] = new Dictionary<int, List<GameObject>>();

            // Initialize for each player
            GameManager gameManager = GameManager.Instance;
            if (gameManager != null)
            {
                for (int playerId = 0; playerId < gameManager.PlayerCount; playerId++)
                {
                    worldShipPieces[worldObject][playerId] = new List<GameObject>();
                }
            }
        }

        // Also get orbit locations from WorldManager (they don't have PlanetBody components)
        if (trackOrbitLocations)
        {
            List<GameObject> orbitLocations = worldManager.GetAllOrbitLocations();
            foreach (GameObject orbitLocation in orbitLocations)
            {
                if (orbitLocation == null) continue;

                // Initialize tracking for this orbit location
                worldShipPieces[orbitLocation] = new Dictionary<int, List<GameObject>>();

                // Initialize for each player
                GameManager gameManager = GameManager.Instance;
                if (gameManager != null)
                {
                    for (int playerId = 0; playerId < gameManager.PlayerCount; playerId++)
                    {
                        worldShipPieces[orbitLocation][playerId] = new List<GameObject>();
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// Check if a world name indicates it's an orbit location
    /// </summary>
    private bool IsOrbitLocation(string worldName)
    {
        // Look for the specific Low[Planet]Orbit pattern
        return worldName.StartsWith("Low") && worldName.EndsWith("Orbit");
    }
    
    /// <summary>
    /// Refresh ship pieces for all players and worlds
    /// </summary>
    public void RefreshAllShipPieces()
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null) return;
        
        // Clear all existing ship pieces
        ClearAllShipPieces();
        
        // Update for each player
        foreach (Player player in gameManager.Players)
        {
            RefreshPlayerShipPieces(player.PlayerId);
        }
    }
    
    /// <summary>
    /// Refresh ship pieces for a specific player
    /// </summary>
    public void RefreshPlayerShipPieces(int playerId)
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null || playerId >= gameManager.PlayerCount) return;
        
        Player player = gameManager.Players[playerId];
        if (player == null) return;
        
        // Clear existing pieces for this player
        ClearPlayerShipPieces(playerId);
        
        // Get all ships for this player
        List<Ship> playerShips = player.GetShips();

        // Group ships by location
        var shipsByLocation = playerShips.GroupBy(ship => ship.CurrentLocation);
        
        foreach (var locationGroup in shipsByLocation)
        {
            GameObject world = locationGroup.Key;
            if (world == null) continue;

            // Skip orbit locations if tracking is disabled
            if (!trackOrbitLocations && IsOrbitLocation(world.name))
                continue;

            // Skip if we're not tracking this world
            if (!worldShipPieces.ContainsKey(world))
                continue;

            List<Ship> shipsAtWorld = locationGroup.ToList();
            CreateShipPiecesAtWorldForPlayer(world, playerId, shipsAtWorld);
        }
    }
    
    /// <summary>
    /// Create ship pieces at a specific world for a player
    /// </summary>
    private void CreateShipPiecesAtWorld(GameObject world, int playerId, int shipCount)
    {
        if (shipCount <= 0) return;

        GameObject shipPrefab = GetShipPrefabForPlayer(playerId);
        if (shipPrefab == null)
        {
            Debug.LogWarning($"No ship prefab found for player {playerId}");
            return;
        }

        Vector3 worldPosition = world.transform.position;
 
        // Calculate starting index based on existing ships from all players at this world
        int startingIndex = 0;
        foreach (var playerEntry in worldShipPieces[world])
        {
            startingIndex += playerEntry.Value.Count;
        }

        bool isOrbit = IsOrbitLocation(world.name);

        for (int i = 0; i < shipCount; i++)
        {
            int actualShipIndex = startingIndex + i;
            Vector3 shipPosition;
            if (isOrbit)
            {
                shipPosition = CalculateOrbitShipPosition(worldPosition, actualShipIndex);
            }
            else
            {
                shipPosition = CalculateShipPosition(worldPosition, actualShipIndex);
            }

            GameObject shipPiece = Instantiate(shipPrefab, shipPosition, Quaternion.identity);
            shipPiece.name = $"ShipPiece_Player{playerId}_{(isOrbit ? "Orbit" : "World")}{world.name}_{actualShipIndex}";
            shipPiece.transform.SetParent(transform); // Parent to this manager

            // Add to tracking
            worldShipPieces[world][playerId].Add(shipPiece);
        }
    }

    /// <summary>
    /// Create ship pieces at a specific world for a player, considering all players' ships for proper positioning
    /// </summary>
    private void CreateShipPiecesAtWorldForPlayer(GameObject world, int playerId, List<Ship> ships)
    {
        if (ships.Count <= 0) return;

        GameObject shipPrefab = GetShipPrefabForPlayer(playerId);
        if (shipPrefab == null)
        {
            Debug.LogWarning($"No ship prefab found for player {playerId}");
            return;
        }

        Vector3 worldPosition = world.transform.position;

        // Get all ships at this world from all players to determine proper indexing
        List<Ship> allShipsAtWorld = new List<Ship>();
        GameManager gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            foreach (Player player in gameManager.Players)
            {
                List<Ship> playerShipsAtWorld = player.GetShipsAtLocation(world);
                allShipsAtWorld.AddRange(playerShipsAtWorld);
            }
        }

        // Since Ship class doesn't have Owner property, we need to determine ship ownership differently
        // We'll create ship pieces directly for the ships we know belong to this player
        bool isOrbit = IsOrbitLocation(world.name);

        // Calculate starting index based on ships from all players at this world
        int totalShipsAtWorld = 0;
        if (gameManager != null)
        {
            foreach (Player player in gameManager.Players)
            {
                List<Ship> playerShipsAtWorld = player.GetShipsAtLocation(world);
                if (player.PlayerId < playerId)
                {
                    // Count ships from players with lower IDs (they come first in positioning)
                    totalShipsAtWorld += playerShipsAtWorld.Count;
                }
            }
        }

        // Create ship pieces for this player's ships
        for (int i = 0; i < ships.Count; i++)
        {
            Ship ship = ships[i];
            int shipIndex = totalShipsAtWorld + i;

            Vector3 shipPosition;
            if (isOrbit)
            {
                shipPosition = CalculateOrbitShipPosition(worldPosition, shipIndex);
            }
            else
            {
                shipPosition = CalculateShipPosition(worldPosition, shipIndex);
            }

            GameObject shipPiece = Instantiate(shipPrefab, shipPosition, Quaternion.identity);
            shipPiece.name = $"ShipPiece_Player{playerId}_{(isOrbit ? "Orbit" : "World")}{world.name}_{shipIndex}";
            shipPiece.transform.SetParent(transform); // Parent to this manager

            // Add to tracking
            worldShipPieces[world][playerId].Add(shipPiece);
        }

    }
    
    /// <summary>
    /// Calculate the position for a ship piece based on world position and ship index
    /// </summary>
    private Vector3 CalculateShipPosition(Vector3 worldPosition, int shipIndex)
    {
        // Ships are arranged in rows of 4, starting at offset (+2, +1, +2.5)
        int row = shipIndex / 4;
        int col = shipIndex % 4;

        float x = worldPosition.x + (2 - col); // Start at +2, then +1, 0, -1
        float y = worldPosition.y + 1;
        float z = worldPosition.z + (2.5f + row); // Start at +2.5, then +3.5, etc.

        Vector3 result = new Vector3(x, y, z);

        return result;
    }

    /// <summary>
    /// Calculate the position for a ship piece at an orbit location based on orbit position and ship index
    /// </summary>
    private Vector3 CalculateOrbitShipPosition(Vector3 orbitPosition, int shipIndex)
    {
        // For orbit locations, ships are arranged differently:
        // First ship at (x, y+1.5, z) from orbit position
        // Multiple ships are spaced 1 apart in x-axis and centered around the orbit position
        // When a row is full (4 ships), start a new row with z+1

        int shipsPerRow = 4;
        int row = shipIndex / shipsPerRow;
        int positionInRow = shipIndex % shipsPerRow;

        // Calculate x offset to center ships around orbit position
        float xOffset;
        if (shipsPerRow == 1)
        {
            xOffset = 0f; // Single ship centered
        }
        else
        {
            // For multiple ships, center them around the orbit position
            // Examples: 2 ships: -0.5, 0.5; 3 ships: -1, 0, 1; 4 ships: -1.5, -0.5, 0.5, 1.5
            float totalWidth = (shipsPerRow - 1) * 1f; // 1 unit spacing between ships
            float startX = -totalWidth / 2f;
            xOffset = startX + (positionInRow * 1f);
        }

        Vector3 result = new Vector3(
            orbitPosition.x + xOffset,
            orbitPosition.y + 1.5f,
            orbitPosition.z + row
        );

        return result;
    }
    
    /// <summary>
    /// Get the ship prefab for a specific player
    /// </summary>
    private GameObject GetShipPrefabForPlayer(int playerId)
    {
        if (playerId >= 0 && playerId < playerShipPrefabs.Length)
        {
            return playerShipPrefabs[playerId];
        }
        
        return null;
    }
    
    /// <summary>
    /// Clear all ship pieces from the map
    /// </summary>
    private void ClearAllShipPieces()
    {
        foreach (var worldEntry in worldShipPieces)
        {
            foreach (var playerEntry in worldEntry.Value)
            {
                foreach (GameObject shipPiece in playerEntry.Value)
                {
                    if (shipPiece != null)
                        Destroy(shipPiece);
                }
                playerEntry.Value.Clear();
            }
        }
    }
    
    /// <summary>
    /// Clear ship pieces for a specific player
    /// </summary>
    private void ClearPlayerShipPieces(int playerId)
    {
        foreach (var worldEntry in worldShipPieces)
        {
            if (worldEntry.Value.ContainsKey(playerId))
            {
                foreach (GameObject shipPiece in worldEntry.Value[playerId])
                {
                    if (shipPiece != null)
                        Destroy(shipPiece);
                }
                worldEntry.Value[playerId].Clear();
            }
        }
    }
    
    /// <summary>
    /// Update ship pieces when a ship is added to a world
    /// </summary>
    public void OnShipAddedToWorld(int playerId, GameObject world)
    {
        RefreshPlayerShipPieces(playerId);
    }
    
    /// <summary>
    /// Update ship pieces when a ship is removed from a world
    /// </summary>
    public void OnShipRemovedFromWorld(int playerId, GameObject world)
    {
        RefreshPlayerShipPieces(playerId);
    }
    
    /// <summary>
    /// Update ship pieces when a ship moves between worlds
    /// </summary>
    public void OnShipMoved(int playerId, GameObject fromWorld, GameObject toWorld)
    {
        RefreshPlayerShipPieces(playerId);
    }
}

using UnityEngine;
using System.Collections.Generic;

public partial class Player
{
    public int PlayerId { get; private set; }
    public int VictoryPoints { get; set; }
    public int ScienceValue { get; set; }
    public int MaxActions { get; set; } = 4; // Start with 4 actions, can be modified by techs/wonders
    public int ActionsRemaining { get; set; } = 4; // Actions remaining in the current turn
    public int MaxShips { get; set; } = 5; // Max ships player can control
    public int ShipsInPlay { get; set; } = 1; // Ships currently in play (not in storage)

    private static GameObject earthReference = null;

    // Dictionary to track resources on each planetBody
    // Key: PlanetBody GameObject, Value: Dictionary of resource types and amounts
    private Dictionary<GameObject, Dictionary<ResourceType, int>> planetaryResources =
        new Dictionary<GameObject, Dictionary<ResourceType, int>>();

    // Dictionary to track modules on each planetBody
    // Key: PlanetBody GameObject, Value: List of modules
    private Dictionary<GameObject, List<Module>> planetaryModules =
        new Dictionary<GameObject, List<Module>>();

    // Player-owned ships (ships can move between planets)
    private List<Ship> ships = new List<Ship>();

    // Player-owned technologies (these are not planet-specific)
    private List<TechnologyCardData> technologies = new List<TechnologyCardData>();

    public Player(int playerId)
    {
        PlayerId = playerId;
        VictoryPoints = 0;
        ScienceValue = 0;
    }

    #region Resource Management

    // Get resource amount on a specific planetBody
    public int GetResourceAmount(GameObject planetBody, ResourceType type)
    {
        // Always use the cached Earth reference if this is Earth
        if (planetBody.name == "Earth" && earthReference != null && planetBody != earthReference)
        {
            planetBody = earthReference; // Use the cached Earth reference instead
        }

        if (!planetaryResources.ContainsKey(planetBody))
            return 0;

        if (!planetaryResources[planetBody].ContainsKey(type))
            return 0;

        return planetaryResources[planetBody][type];
    }

    public bool HasResourcesAtPlanet(GameObject planet)
    {
        return planetaryResources.ContainsKey(planet) && planetaryResources[planet].Count > 0;
    }

    public void ClearResourcesAtPlanet(GameObject planet)
    {
        if (planetaryResources.ContainsKey(planet))
        {
            planetaryResources[planet].Clear();
        }
    }

    // Add resources to a specific location (planet or orbit)
    public void AddResource(GameObject location, ResourceType type, int amount)
    {
        if (location == null)
        {
            Debug.LogError("Cannot add resource to null location!");
            return;
        }

        // Create dictionaries if they don't exist
        if (!planetaryResources.ContainsKey(location))
        {
            planetaryResources[location] = new Dictionary<ResourceType, int>();

            // When adding resources to a new location, also add the world card to player's play area
            PlayAreaManager playAreaManager = PlayAreaManager.Instance;
            if (playAreaManager != null)
            {
                PlayerPlayArea playerArea = playAreaManager.GetPlayerPlayArea(PlayerId);
                if (playerArea != null)
                {
                    playerArea.AddWorldCard(location);
                }
            }
        }

        if (!planetaryResources[location].ContainsKey(type))
        {
            planetaryResources[location][type] = 0;
        }

        planetaryResources[location][type] += amount;
    }


    // Use resources from a specific location (planet or orbit)
    public bool UseResource(GameObject location, ResourceType type, int amount)
    {
        // Check if we have enough of the resource at this location
        if (!planetaryResources.ContainsKey(location) ||
            !planetaryResources[location].ContainsKey(type) ||
            planetaryResources[location][type] < amount)
        {
            Debug.LogWarning($"Player {PlayerId} doesn't have enough {type} at {location.name}");
            return false;
        }

        // Use the resource
        planetaryResources[location][type] -= amount;
        return true;
    }

    // Transfer resources between locations (planets or orbits)
    // Returns false if the 'from' location doesn't have enough resources
    public bool TransferResource(GameObject fromLocation, GameObject toLocation, ResourceType type, int amount)
    {
        // Verify source has enough resources
        if (!UseResource(fromLocation, type, amount))
            return false;

        // Add resources to destination
        AddResource(toLocation, type, amount);

        Debug.Log($"Player {PlayerId} transferred {amount} {type} from {fromLocation.name} to {toLocation.name}");
        return true;
    }

    // Get all locations where this player has resources (planets and orbits)
    public List<GameObject> GetPlanetBodiesWithResources()
    {
        return new List<GameObject>(planetaryResources.Keys);
    }

    // Get all resources at a specific location (planet or orbit)
    public Dictionary<ResourceType, int> GetResourcesOnPlanet(GameObject location)
    {

        if (!planetaryResources.ContainsKey(location))
        {
            return new Dictionary<ResourceType, int>();
        }

        Dictionary<ResourceType, int> resources = new Dictionary<ResourceType, int>(planetaryResources[location]);
        return resources;
    }

    #endregion

    #region Module Management

    // Add a module to a specific location (planet or orbit)
    public void AddModule(GameObject location, Module module)
    {
        if (!planetaryModules.ContainsKey(location))
        {
            planetaryModules[location] = new List<Module>();
        }

        planetaryModules[location].Add(module);
    }

    // Get all modules at a specific location (planet or orbit)
    public List<Module> GetModulesOnPlanet(GameObject location)
    {
        if (!planetaryModules.ContainsKey(location))
            return new List<Module>();

        return new List<Module>(planetaryModules[location]);
    }

    // Count modules of a specific type at a location (planet or orbit)
    public int CountModulesOfType(GameObject location, ModuleType type)
    {
        if (!planetaryModules.ContainsKey(location))
            return 0;

        int count = 0;
        foreach (Module module in planetaryModules[location])
        {
            if (module.Type == type)
                count++;
        }

        return count;
    }

    // Get total power available at a location (planet or orbit)
    public int GetTotalPowerOnPlanet(GameObject location)
    {
        if (!planetaryModules.ContainsKey(location))
            return 0;

        int totalPower = 0;
        foreach (Module module in planetaryModules[location])
        {
            if (module.Type == ModuleType.Power)
                totalPower += module.PowerOutput;
        }

        return totalPower;
    }

    // Get all locations where this player has modules (planets and orbits)
    public List<GameObject> GetPlanetBodiesWithModules()
    {
        return new List<GameObject>(planetaryModules.Keys);
    }

    #endregion

    #region Ship Management

    // Add a ship to the player's fleet
    public void AddShip(Ship ship)
    {
        ship.Owner = this; // Set owner reference
        ships.Add(ship);
    }

    // Get all ships owned by this player
    public List<Ship> GetShips()
    {
        return new List<Ship>(ships);
    }


    // Get ships at a specific location
    public List<Ship> GetShipsAtLocation(GameObject location)
    {
        List<Ship> shipsAtLocation = new List<Ship>();

        foreach (Ship ship in ships)
        {
            if (ship.CurrentLocation == location)
                shipsAtLocation.Add(ship);
        }

        return shipsAtLocation;
    }

    /// <summary>
    /// Move a ship from one location to another
    /// </summary>
    public bool MoveShip(Ship ship, GameObject newLocation)
    {
        if (ship == null || newLocation == null)
            return false;

        if (!ships.Contains(ship))
        {
            Debug.LogWarning($"Player {PlayerId} doesn't own ship {ship.Name}");
            return false;
        }

        GameObject oldLocation = ship.CurrentLocation;
        ship.CurrentLocation = newLocation;

        // Update ship pieces on map
        if (ShipMapTracker.Instance != null)
        {
            ShipMapTracker.Instance.OnShipMoved(PlayerId, oldLocation, newLocation);
        }

        Debug.Log($"Player {PlayerId} moved ship {ship.Name} from {oldLocation?.name} to {newLocation.name}");
        return true;
    }

    #endregion

    #region Technology Management

    // Add a technology to the player
    public void AddTechnology(TechnologyCardData technology)
    {
        technologies.Add(technology);

        // Apply technology effects
        ApplyTechnologyEffects(technology);
    }

    // Check if player has a specific technology
    public bool HasTechnology(string technologyName)
    {
        foreach (TechnologyCardData tech in technologies)
        {
            if (tech.cardName == technologyName)
                return true;
        }

        return false;
    }

    // Get all technologies owned by this player
    public List<TechnologyCardData> GetTechnologies()
    {
        return new List<TechnologyCardData>(technologies);
    }

    // Apply effects from a technology card
    private void ApplyTechnologyEffects(TechnologyCardData technology)
    {

    }

    #endregion

    // Calculate total victory points (including points from wonders, etc.)
    public int CalculateTotalVictoryPoints()
    {
        int total = VictoryPoints;

        // Add points from modules (like wonders)
        foreach (var planetModulesPair in planetaryModules)
        {
            foreach (Module module in planetModulesPair.Value)
            {
                if (module.IsWonder)
                    total += module.VictoryPointValue;
            }
        }

        return total;
    }

    /// <summary>
    /// Remove a module from a specific location (planet or orbit)
    /// </summary>
    public bool RemoveModule(GameObject location, string moduleName)
    {
        if (!planetaryModules.ContainsKey(location))
            return false;

        List<Module> modules = planetaryModules[location];
        Module moduleToRemove = null;

        foreach (Module module in modules)
        {
            if (module.Name == moduleName)
            {
                moduleToRemove = module;
                break;
            }
        }

        if (moduleToRemove != null)
        {
            modules.Remove(moduleToRemove);
            Debug.Log($"Player {PlayerId} removed {moduleName} module from {location.name}");
            return true;
        }

        return false;
    }

    /// <summary>
    /// Remove a ship from the player's fleet
    /// </summary>
    public bool RemoveShip(string shipName, GameObject location)
    {
        Ship shipToRemove = null;

        foreach (Ship ship in ships)
        {
            if (ship.Name == shipName && ship.CurrentLocation == location)
            {
                shipToRemove = ship;
                break;
            }
        }

        if (shipToRemove != null)
        {
            ships.Remove(shipToRemove);
            Debug.Log($"Player {PlayerId} removed ship {shipName} from {location.name}");

            // Update ship pieces on map
            if (ShipMapTracker.Instance != null)
            {
                ShipMapTracker.Instance.OnShipRemovedFromWorld(PlayerId, location);
            }

            return true;
        }

        return false;
    }
}

// Expanded class definitions needed by Player class

public enum ModuleType
{
    Power,
    Extractor,
    Processor,
    Wonder,
    Other
}

public class Module
{
    public string Name { get; set; }
    public ModuleType Type { get; set; }
    public int PowerOutput { get; set; } = 0;    // For power modules
    public int PowerRequired { get; set; } = 0;  // Power needed to activate
    public bool IsWonder { get; set; } = false;
    public int VictoryPointValue { get; set; } = 0;
    public string ProcessorDescription { get; set; }
    public bool IsActive { get; set; } = false; // For activated modules
    public int Tier { get; set; } = 0;
    // Additional module properties can be added based on game rules
}

public class Ship
{
    public string Name { get; set; }
    public GameObject CurrentLocation { get; set; }
    public float DeltaVPerFuel { get; set; }
    public int Strength { get; set; } = 0;
    public int CargoCapacity { get; set; }
    public bool IsConsumedOnSurvey { get; set; } = false; // If true, ship is consumed when surveying a planet

    // Owner reference for ship map tracking
    public Player Owner { get; set; }

    // Cargo storage dictionary
    private Dictionary<ResourceType, int> cargo = new Dictionary<ResourceType, int>();

    // Cargo management methods
    public bool AddCargo(ResourceType type, int amount)
    {
        // Check total cargo capacity
        int currentCargo = GetTotalCargoAmount();
        if (currentCargo + amount > CargoCapacity)
            return false;

        if (!cargo.ContainsKey(type))
            cargo[type] = 0;

        cargo[type] += amount;
        return true;
    }

    public bool RemoveCargo(ResourceType type, int amount)
    {
        if (!cargo.ContainsKey(type) || cargo[type] < amount)
            return false;

        cargo[type] -= amount;
        return true;
    }

    public int GetCargoAmount(ResourceType type)
    {
        return cargo.ContainsKey(type) ? cargo[type] : 0;
    }

    public int GetTotalCargoAmount()
    {
        int total = 0;
        foreach (var resource in cargo.Values)
            total += resource;
        return total;
    }

    public Dictionary<ResourceType, int> GetAllCargo()
    {
        return new Dictionary<ResourceType, int>(cargo);
    }
}